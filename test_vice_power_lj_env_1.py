#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用lj_env_1环境测试副功率预测模型
测试数据：完整测试数据_含输入特征.csv
"""

import os
import sys
import pandas as pd
import numpy as np
import warnings
from pathlib import Path
import time
from datetime import datetime

# 忽略警告
warnings.filterwarnings('ignore')

# 添加项目路径到sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_lj_env_1_environment():
    """设置lj_env_1运行环境"""
    print("🔧 设置lj_env_1运行环境...")
    
    # 设置环境变量
    os.environ['MODEL_ENV'] = 'lj_env_1'
    os.environ['FLASK_ENV'] = 'development'
    
    # 设置模型路径
    models_dir = project_root / "kongwen_power_control/beta_version/v6/production_deployment/models"
    os.environ['MODELS_DIR'] = str(models_dir)
    
    print(f"✅ 环境变量设置完成:")
    print(f"   MODEL_ENV: {os.environ.get('MODEL_ENV')}")
    print(f"   MODELS_DIR: {os.environ.get('MODELS_DIR')}")
    print(f"   项目根目录: {project_root}")
    
    return models_dir

def load_test_data():
    """加载测试数据"""
    print("\n📊 加载测试数据...")
    
    csv_file = project_root / "完整测试数据_含输入特征.csv"
    
    if not csv_file.exists():
        raise FileNotFoundError(f"测试数据文件不存在: {csv_file}")
    
    df = pd.read_csv(csv_file)
    print(f"✅ 测试数据加载成功: {len(df)} 条记录")
    print(f"   数据列: {list(df.columns)}")
    
    # 显示数据基本信息
    print(f"\n📈 数据基本信息:")
    print(f"   重量差异范围: {df['weight_difference'].min():.2f} - {df['weight_difference'].max():.2f} kg")
    print(f"   硅热能范围: {df['silicon_thermal_energy_kwh'].min():.2f} - {df['silicon_thermal_energy_kwh'].max():.2f} kWh")
    print(f"   工艺类型分布: {df['process_type'].value_counts().to_dict()}")
    
    return df

def test_lj_env_1_model(models_dir, test_data):
    """测试lj_env_1环境下的副功率预测模型"""
    print("\n🧪 开始测试lj_env_1副功率预测模型...")
    
    try:
        # 导入lj_env_1预测器
        sys.path.insert(0, str(models_dir.parent / "src"))
        from predict import VicePowerPredictor, predict_vice_power
        
        print("✅ lj_env_1预测器导入成功")
        
        # 初始化预测器
        predictor = VicePowerPredictor(model_path=str(models_dir))
        print("✅ lj_env_1预测器初始化成功")
        
        # 测试单个预测
        print("\n🔍 测试单个预测...")
        test_sample = test_data.iloc[0]
        
        weight_diff = test_sample['weight_difference']
        silicon_energy = test_sample['silicon_thermal_energy_kwh']
        process_type = test_sample['process_type']
        actual_power = test_sample['actual_vice_power']
        
        print(f"   测试样本:")
        print(f"   - 重量差异: {weight_diff:.2f} kg")
        print(f"   - 硅热能: {silicon_energy:.2f} kWh")
        print(f"   - 工艺类型: {process_type}")
        print(f"   - 实际副功率: {actual_power:.2f} kWh")
        
        # 进行预测
        start_time = time.time()
        result = predictor.predict_single(weight_diff, silicon_energy, process_type)
        prediction_time = time.time() - start_time
        
        print(f"\n📊 预测结果:")
        print(f"   - 预测副功率: {result['predicted_vice_power_kwh']:.2f} kWh")
        print(f"   - 使用模型: {result.get('model_used', 'Unknown')}")
        print(f"   - 预测时间: {prediction_time:.4f} 秒")
        print(f"   - 预测误差: {abs(result['predicted_vice_power_kwh'] - actual_power):.2f} kWh")
        
        return predictor, result
        
    except Exception as e:
        print(f"❌ 模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def batch_test_model(predictor, test_data, sample_size=50):
    """批量测试模型性能"""
    print(f"\n🔄 批量测试模型性能 (样本数: {sample_size})...")
    
    if predictor is None:
        print("❌ 预测器不可用，跳过批量测试")
        return
    
    # 随机采样测试数据
    if len(test_data) > sample_size:
        test_sample = test_data.sample(n=sample_size, random_state=42)
    else:
        test_sample = test_data
    
    predictions = []
    errors = []
    processing_times = []
    
    print("正在进行批量预测...")
    
    for idx, row in test_sample.iterrows():
        try:
            start_time = time.time()
            
            result = predictor.predict_single(
                row['weight_difference'],
                row['silicon_thermal_energy_kwh'],
                row['process_type']
            )
            
            processing_time = time.time() - start_time
            processing_times.append(processing_time)
            
            predicted_power = result['predicted_vice_power_kwh']
            actual_power = row['actual_vice_power']
            error = abs(predicted_power - actual_power)
            
            predictions.append(predicted_power)
            errors.append(error)
            
            if len(predictions) % 10 == 0:
                print(f"   已完成 {len(predictions)}/{len(test_sample)} 个预测")
                
        except Exception as e:
            print(f"   预测失败 (索引 {idx}): {e}")
            continue
    
    # 计算性能指标
    if predictions:
        mae = np.mean(errors)
        rmse = np.sqrt(np.mean([e**2 for e in errors]))
        accuracy_7kwh = sum(1 for e in errors if e <= 7) / len(errors) * 100
        avg_processing_time = np.mean(processing_times)
        
        print(f"\n📈 批量测试结果:")
        print(f"   - 测试样本数: {len(predictions)}")
        print(f"   - 平均绝对误差 (MAE): {mae:.2f} kWh")
        print(f"   - 均方根误差 (RMSE): {rmse:.2f} kWh")
        print(f"   - ±7kWh准确率: {accuracy_7kwh:.1f}%")
        print(f"   - 平均预测时间: {avg_processing_time:.4f} 秒")
        
        # 误差分布分析
        error_ranges = {
            '≤3kWh': sum(1 for e in errors if e <= 3),
            '3-7kWh': sum(1 for e in errors if 3 < e <= 7),
            '7-15kWh': sum(1 for e in errors if 7 < e <= 15),
            '>15kWh': sum(1 for e in errors if e > 15)
        }
        
        print(f"\n📊 误差分布:")
        for range_name, count in error_ranges.items():
            percentage = count / len(errors) * 100
            print(f"   - {range_name}: {count} 个 ({percentage:.1f}%)")
    
    else:
        print("❌ 没有成功的预测结果")

def test_compatibility_function(models_dir, test_data):
    """测试兼容性函数"""
    print("\n🔗 测试兼容性函数...")
    
    try:
        sys.path.insert(0, str(models_dir.parent / "src"))
        from predict import predict_vice_power
        
        # 测试兼容性函数
        test_sample = test_data.iloc[0]
        
        result = predict_vice_power(
            weight_difference=test_sample['weight_difference'],
            silicon_thermal_energy_kwh=test_sample['silicon_thermal_energy_kwh'],
            process_type=test_sample['process_type'],
            model_path=str(models_dir)
        )
        
        print(f"✅ 兼容性函数测试成功:")
        print(f"   - 预测结果: {result['predicted_vice_power_kwh']:.2f} kWh")
        print(f"   - 使用模型: {result.get('model_used', 'Unknown')}")
        
    except Exception as e:
        print(f"❌ 兼容性函数测试失败: {e}")

def main():
    """主函数"""
    print("🚀 开始lj_env_1环境副功率预测模型测试")
    print("=" * 60)
    
    try:
        # 1. 设置lj_env_1环境
        models_dir = setup_lj_env_1_environment()
        
        # 2. 加载测试数据
        test_data = load_test_data()
        
        # 3. 测试模型
        predictor, single_result = test_lj_env_1_model(models_dir, test_data)
        
        # 4. 批量测试
        if predictor:
            batch_test_model(predictor, test_data, sample_size=100)
        
        # 5. 测试兼容性函数
        test_compatibility_function(models_dir, test_data)
        
        print("\n" + "=" * 60)
        print("✅ lj_env_1环境副功率预测模型测试完成")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
